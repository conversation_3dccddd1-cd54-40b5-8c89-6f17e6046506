Dependencies for Project 'empty_LP_MSPM0G3507_nortos_keil', Target 'empty_LP_MSPM0G3507_nortos_keil': (DO NOT MODIFY !)
CompilerVersion: 6210000::V6.21::ARMCLANG
F (../empty.c)(0x686B699D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../empty-8-I2C -I ../../source -I ./HardWear

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/empty.o -MD)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h.ardWear\software_iic.h)(0x00000000)
I (..\..\empty-8-I2C\ti_msp_dl_config.htardWear\gw_color_sensor.h)(0x00000000)
I (C:\Keil_v5\ARM\ARMCLANG\include\time.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\string.hWardWear\LED.hsardWear\KEY.h)(0x00000000)
I (HardWear\USART.hRardWear\PWM.heardWear\hw_motor.h)(0x00000000)
I (HardWear\hw_encoder.h)(0x686A410C)
F (../empty.syscfg)(0x68691078)()
F (startup_mspm0g350x_uvision.s)(0x6867B55C)(--target=arm-arm-none-eabi -mcpu=cortex-m0plus -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4

-Wa,armasm,--pd,"__UVISION_VERSION SETA 539" -Wa,armasm,--pd,"__MSPM0G3507__ SETA 1"

-o ./objects/startup_mspm0g350x_uvision.o)
F (../ti_msp_dl_config.h)(0x686A407F)()
F (../ti_msp_dl_config.c)(0x68691086)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../empty-8-I2C -I ../../source -I ./HardWear

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ti_msp_dl_config.o -MD)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
F (.\HardWear\LED.c)(0x68651DD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../empty-8-I2C -I ../../source -I ./HardWear

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/led.o -MD)
I (..\..\empty-8-I2C\ti_msp_dl_config.ht.\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
F (.\HardWear\LED.h)(0x6864FEF2)()
F (.\HardWear\KEY.c)(0x6868E0A0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../empty-8-I2C -I ../../source -I ./HardWear

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/key.o -MD)
I (..\..\empty-8-I2C\ti_msp_dl_config.ht.\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h.ardWear\LED.h)(0x00000000)
F (.\HardWear\KEY.h)(0x68661204)()
F (.\HardWear\USART.c)(0x68662A3B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../empty-8-I2C -I ../../source -I ./HardWear

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/usart.o -MD)
I (..\..\empty-8-I2C\ti_msp_dl_config.ht.\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x6569B012)
F (.\HardWear\USART.h)(0x6865E945)()
F (.\HardWear\PWM.c)(0x68663D6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../empty-8-I2C -I ../../source -I ./HardWear

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/pwm.o -MD)
I (..\..\empty-8-I2C\ti_msp_dl_config.ho.\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h.ardWear\LED.h)(0x00000000)
F (.\HardWear\PWM.h)(0x68663D6A)()
F (.\HardWear\gw_color_sensor.h)(0x686784A3)()
F (.\HardWear\gw_grayscale_sensor.h)(0x686784A4)()
F (.\HardWear\IIC.c)(0x686784A1)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../empty-8-I2C -I ../../source -I ./HardWear

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/iic.o -MD)
I (..\..\empty-8-I2C\ti_msp_dl_config.ht.\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x6569B012)
I (HardWear\gw_grayscale_sensor.h)(0x686784A4)
F (.\HardWear\IIC.h)(0x68678497)()
F (.\HardWear\software_iic.c)(0x6867BDB0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../empty-8-I2C -I ../../source -I ./HardWear

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/software_iic.o -MD)
I (..\..\empty-8-I2C\ti_msp_dl_config.hw.\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h.ardWear\gw_color_sensor.h)(0x00000000)
I (C:\Keil_v5\ARM\ARMCLANG\include\time.h)(0x6569B012)
F (.\HardWear\software_iic.h)(0x6867B964)()
F (.\HardWear\hw_motor.c)(0x686A546B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../empty-8-I2C -I ../../source -I ./HardWear

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/hw_motor.o -MD)
I (..\..\empty-8-I2C\ti_msp_dl_config.h..\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
F (.\HardWear\hw_motor.h)(0x686906BC)()
F (.\HardWear\hw_encoder.c)(0x686A70EA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../empty-8-I2C -I ../../source -I ./HardWear

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/hw_encoder.o -MD)
I (..\..\empty-8-I2C\ti_msp_dl_config.ho.\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (D:\Ti\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
F (.\HardWear\hw_encoder.h)(0x686A410C)()
