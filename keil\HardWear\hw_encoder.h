#ifndef _HW_ENCODER_H_
#define _HW_ENCODER_H_

#include "ti_msp_dl_config.h"

typedef enum
{
    FORWARD, // 正向
    REVERSAL // 反向
} ENCODER_DIR;

typedef struct
{
    volatile long long temp_count; // 保存实时计数值
    int count;                     // 根据定时器时间更新的计数值
    ENCODER_DIR dir;               // 旋转方向
} ENCODER_RES;

void GROUP1_IRQHandler(void);
void encoder_init();

void right_encoder_update(void);
ENCODER_DIR get_right_encoder_dir(void);
int get_right_encoder_count(void);

void left_encoder_update(void);
ENCODER_DIR get_left_encoder_dir(void);
int get_left_encoder_count(void);

// 调试函数
uint32_t get_left_interrupt_count(void);
uint32_t get_right_interrupt_count(void);
void reset_interrupt_counts(void);

#endif