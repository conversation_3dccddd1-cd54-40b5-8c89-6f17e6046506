/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.23.1+4034"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const GPIO4   = GPIO.addInstance();
const GPIO5   = GPIO.addInstance();
const GPIO6   = GPIO.addInstance();
const GPIO7   = GPIO.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const PWM2    = PWM.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER   = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1  = TIMER.addInstance();
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

GPIO1.port                               = "PORTB";
GPIO1.$name                              = "LED1";
GPIO1.associatedPins[0].$name            = "PIN_22";
GPIO1.associatedPins[0].assignedPin      = "22";
GPIO1.associatedPins[0].internalResistor = "PULL_DOWN";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                              = "KEY1";
GPIO2.port                               = "PORTB";
GPIO2.associatedPins[0].$name            = "PIN_21";
GPIO2.associatedPins[0].direction        = "INPUT";
GPIO2.associatedPins[0].internalResistor = "PULL_UP";
GPIO2.associatedPins[0].assignedPin      = "21";
GPIO2.associatedPins[0].interruptEn      = true;
GPIO2.associatedPins[0].polarity         = "RISE";

GPIO3.$name                         = "Software_iic";
GPIO3.associatedPins.create(2);
GPIO3.associatedPins[0].$name       = "SDA";
GPIO3.associatedPins[0].ioStructure = "OD";
GPIO3.associatedPins[0].pin.$assign = "PA1";
GPIO3.associatedPins[1].$name       = "SCL";
GPIO3.associatedPins[1].ioStructure = "OD";

GPIO4.$name                         = "MOTORA";
GPIO4.port                          = "PORTB";
GPIO4.associatedPins.create(2);
GPIO4.associatedPins[0].$name       = "AIN1";
GPIO4.associatedPins[0].assignedPin = "12";
GPIO4.associatedPins[0].pin.$assign = "PB12";
GPIO4.associatedPins[1].$name       = "AIN2";
GPIO4.associatedPins[1].assignedPin = "13";
GPIO4.associatedPins[1].pin.$assign = "PB13";

GPIO5.$name                         = "MOTORB";
GPIO5.port                          = "PORTB";
GPIO5.associatedPins.create(2);
GPIO5.associatedPins[0].$name       = "BIN1";
GPIO5.associatedPins[0].assignedPin = "24";
GPIO5.associatedPins[0].pin.$assign = "PB24";
GPIO5.associatedPins[1].$name       = "BIN2";
GPIO5.associatedPins[1].assignedPin = "25";
GPIO5.associatedPins[1].pin.$assign = "PB25";

GPIO6.port                               = "PORTA";
GPIO6.$name                              = "Encoder_1";
GPIO6.associatedPins.create(2);
GPIO6.associatedPins[0].$name            = "E1A";
GPIO6.associatedPins[0].direction        = "INPUT";
GPIO6.associatedPins[0].assignedPin      = "12";
GPIO6.associatedPins[0].interruptEn      = true;
GPIO6.associatedPins[0].polarity         = "RISE";
GPIO6.associatedPins[1].direction        = "INPUT";
GPIO6.associatedPins[1].internalResistor = "PULL_UP";
GPIO6.associatedPins[1].assignedPin      = "8";
GPIO6.associatedPins[1].$name            = "E1B";

GPIO7.$name                              = "Encoder_2";
GPIO7.port                               = "PORTB";
GPIO7.associatedPins.create(2);
GPIO7.associatedPins[0].$name            = "E2A";
GPIO7.associatedPins[0].direction        = "INPUT";
GPIO7.associatedPins[0].interruptEn      = true;
GPIO7.associatedPins[0].polarity         = "RISE";
GPIO7.associatedPins[0].assignedPin      = "4";
GPIO7.associatedPins[1].$name            = "E2B";
GPIO7.associatedPins[1].direction        = "INPUT";
GPIO7.associatedPins[1].internalResistor = "PULL_UP";
GPIO7.associatedPins[1].assignedPin      = "5";

PWM1.$name                              = "PWM_LED";
PWM1.clockDivider                       = 8;
PWM1.ccIndex                            = [1];
PWM1.timerStartTimer                    = true;
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";
PWM1.peripheral.$assign                 = "TIMG6";
PWM1.peripheral.ccp1Pin.$assign         = "PB27";
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC0";

PWM2.$name                              = "PWM_MOTOR";
PWM2.timerStartTimer                    = true;
PWM2.clockDivider                       = 8;
PWM2.peripheral.$assign                 = "TIMG7";
PWM2.peripheral.ccp0Pin.$assign         = "PA26";
PWM2.peripheral.ccp1Pin.$assign         = "PA27";
PWM2.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM2.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC2";
PWM2.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";
PWM2.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric4";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

SYSTICK.periodEnable  = true;
SYSTICK.systickEnable = true;
SYSTICK.period        = 32;

TIMER1.$name              = "TIMER_0";
TIMER1.timerClkDiv        = 8;
TIMER1.timerClkPrescale   = 100;
TIMER1.timerStartTimer    = true;
TIMER1.interrupts         = ["ZERO"];
TIMER1.timerMode          = "PERIODIC";
TIMER1.timerPeriod        = "1ms";
TIMER1.peripheral.$assign = "TIMG0";

UART1.$name                    = "UART_0";
UART1.uartClkSrc               = "MFCLK";
UART1.rxTimeoutValue           = 1;
UART1.enabledInterrupts        = ["RX","RX_TIMEOUT_ERROR"];
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
GPIO1.associatedPins[0].pin.$suggestSolution = "PB22";
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
GPIO2.associatedPins[0].pin.$suggestSolution = "PB21";
GPIO3.associatedPins[1].pin.$suggestSolution = "PA0";
GPIO6.associatedPins[0].pin.$suggestSolution = "PA12";
GPIO6.associatedPins[1].pin.$suggestSolution = "PA8";
GPIO7.associatedPins[0].pin.$suggestSolution = "PB4";
GPIO7.associatedPins[1].pin.$suggestSolution = "PB5";
UART1.peripheral.$suggestSolution            = "UART0";
