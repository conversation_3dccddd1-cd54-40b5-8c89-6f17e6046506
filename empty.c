/*
 * Copyright (c) 2021, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/************************* 头文件 *************************/
#include "ti_msp_dl_config.h"
#include "software_iic.h"
#include "stdio.h"
#include "string.h"

#include "LED.h"
#include "KEY.h"
#include "USART.h"
#include "PWM.h"
#include "hw_motor.h"
#include "hw_encoder.h"

#if !defined(__MICROLIB)
// 不使用微库的话就需要添加下面的函数
#if (__ARMCLIB_VERSION <= 6000000)
// 如果编译器是AC5  就定义下面这个结构体

struct __FILE
{
    int handle;
};

#endif
FILE __stdout;
// 定义_sys_exit()以避免使用半主机模式
void _sys_exit(int x)
{
    x = x;
}
#endif

/************************* 宏定义 *************************/
#define delay_ms(X) delay_cycles((CPUCLK_FREQ / 1000) * (X));

/* 默认地址 */
#define GW_GRAY_ADDR_DEF 0x4C
#define GW_GRAY_PING 0xAA
#define GW_GRAY_PING_OK 0x66
#define GW_GRAY_PING_RSP GW_GRAY_PING_OK

/* 开启开关数据模式 */
#define GW_GRAY_DIGITAL_MODE 0xDD

/* 开启连续读取模拟数据模式 */
#define GW_GRAY_ANALOG_BASE_ 0xB0
#define GW_GRAY_ANALOG_MODE (GW_GRAY_ANALOG_BASE_ + 0)
/************************ 变量定义 ************************/
volatile uint8_t led_flash_time = 0;

volatile unsigned int delay_times = 0;
volatile unsigned char uart_data = 0;

extern uint8_t IIC_ReadBytes(uint8_t Salve_Address, uint8_t Reg_Address,
                             uint8_t *Result, uint8_t len);
/*
全局变量
*/
uint8_t rx_buff[256] = {0};       // uart1打印用的，不用管，不要动
uint8_t IIC_write_buff[10] = {0}; // IIC写数据用的buff
uint8_t Anolog[8] = {0};          // 1-8路模拟量数据
uint8_t Normal[8] = {0};          // 归一化后的1-8路模拟量数据
uint8_t Digtal;

/************************ 函数定义 ************************/
void PWM_LED(void);

int main(void)
{
    SYSCFG_DL_init();
    //    //使能外部中断
    //		NVIC_EnableIRQ (KEY1_INT_IRQN );

    // 清除定时器中断标志
    NVIC_ClearPendingIRQ(TIMER_0_INST_INT_IRQN);
    // 使能定时器中断
    NVIC_EnableIRQ(TIMER_0_INST_INT_IRQN);

    // 清除串口中断标志
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);
    // 使能串口中断
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);

    encoder_init();
    set_motor(999, 900);
    while (1)
    {
        // 更新编码器计数值
        left_encoder_update();
        right_encoder_update();

        printf("le_pwm:%d ri_pwm:%d\r\n", DL_TimerG_getCaptureCompareValue(PWM_MOTOR_INST, GPIO_PWM_MOTOR_C0_IDX), DL_TimerG_getCaptureCompareValue(PWM_MOTOR_INST, GPIO_PWM_MOTOR_C1_IDX));
        printf("le_code:%d ri_code:%d\r\n", get_left_encoder_count(), get_right_encoder_count());
        printf("le_int:%lu ri_int:%lu\r\n", get_left_interrupt_count(), get_right_interrupt_count());
        printf("---\r\n");

        // 添加延时，避免打印过快
        delay_ms(500); // 增加延时到500ms，便于观察
    }

    //		sprintf((char *)rx_buff,"hello_world!\r\n");
    //		uart0_send_string((char *)rx_buff);
    //		memset(rx_buff,0,256);
    //		while(Ping())
    //		{
    //			delay_ms(1);
    //			sprintf((char *)rx_buff,"Ping Faild Try Again!\r\n");
    //			uart0_send_string((char *)rx_buff);
    //			memset(rx_buff,0,256);
    //		}
    //		sprintf((char *)rx_buff,"Ping Succseful!\r\n");
    //		uart0_send_string((char *)rx_buff);
    //		memset(rx_buff,0,256);
    //		while (1)
    //		{
    //			/*数字量*/
    //			/*数字量数据和1-8号指示灯相同，首先需要校准，才能正常读取*/
    //			/*数字量读取方法1：比较费时，但易于理解，其他更高效方法请看文档*/
    //			IIC_ReadBytes(GW_GRAY_ADDR_DEF << 1/* 默认不插AD0 AD1地址0x4c */, GW_GRAY_DIGITAL_MODE/* 0xDD */, &Digtal/*数据存放在Digtal里*/, 1 /*只读1个数据*/);
    //
    //			/*模拟量*/
    //			/*连续通道模拟量读取与校准无关*/
    //			/*在同一个白场下，模拟量数据会有所差异，这是正常现象，如果您想要数据相同，请校准后打开归一化*/
    //			/*连续通道模拟量读取方法1：比较费时，但易于理解，其他更高效方法请看文档*/
    //			IIC_ReadBytes(GW_GRAY_ADDR_DEF << 1, GW_GRAY_ANALOG_MODE/* 0xB0 */, &Anolog[0] /*数据存放在Anolog里*/, 8 /*读8个数据*/);
    //
    //
    //			/*归一化*/
    //			/*归一化的意思是，使所有探头，在同一个白色或者黑色下，数据是一致的*/
    //			/*这个与校准是有关的，他是通过校准数据给模拟量进行软件处理归一化的*/
    //			IIC_write_buff[0]=GW_GRAY_ANALOG_NORMALIZE;//归一化使能寄存器，掉电不存储
    //			IIC_write_buff[1]=0xff;//全通道开启
    //			IIC_WriteBytes(GW_GRAY_ADDR_DEF << 1,GW_GRAY_ANALOG_NORMALIZE ,&IIC_write_buff[1]/*命令+数据*/, 2 /*写入两个数据*/);
    //			delay_ms(10);//设置完，需要等上一会。stm8的运算速度没stm32快，等一下，让传感器把数据刷新一下。
    //			IIC_ReadBytes(GW_GRAY_ADDR_DEF << 1, GW_GRAY_ANALOG_MODE, &Normal[0]/*打开归一化后读到的数据存在Normalize里*/ , 8 );//跟上面连续通道模拟量读取一样
    //			IIC_write_buff[0]=GW_GRAY_ANALOG_NORMALIZE;
    //			IIC_write_buff[1]=0x00;//全通道关闭
    //			IIC_WriteBytes(GW_GRAY_ADDR_DEF << 1,GW_GRAY_ANALOG_NORMALIZE,&IIC_write_buff[1]/*命令+数据*/, 2 /*写入两个数据*/);//为了while(1)循环继续，当然是要关掉归一化的。
    //
    //			delay_ms(10);
    //			/*打印数据*/
    //			sprintf((char *)rx_buff,"Digtal %d-%d-%d-%d-%d-%d-%d-%d\r\n",(Digtal>>0)&0x01,(Digtal>>1)&0x01,(Digtal>>2)&0x01,(Digtal>>3)&0x01,(Digtal>>4)&0x01,(Digtal>>5)&0x01,(Digtal>>6)&0x01,(Digtal>>7)&0x01);
    //			uart0_send_string((char *)rx_buff);
    //			memset(rx_buff,0,256);
    //
    //			sprintf((char *)rx_buff,"Anolog %d-%d-%d-%d-%d-%d-%d-%d\r\n",Anolog[0],Anolog[1],Anolog[2],Anolog[3],Anolog[4],Anolog[5],Anolog[6],Anolog[7]);
    //			uart0_send_string((char *)rx_buff);
    //			memset(rx_buff,0,256);
    //
    //			sprintf((char *)rx_buff,"Normalize %d-%d-%d-%d-%d-%d-%d-%d\r\n",Normal[0],Normal[1],Normal[2],Normal[3],Normal[4],Normal[5],Normal[6],Normal[7]);
    //			uart0_send_string((char *)rx_buff);
    //			memset(rx_buff,0,256);
    //			delay_ms(1);
    //		}
}

////按键触发外部中断
// void GROUP1_IRQHandler(void)
//{
//     //读取Group1的中断寄存器并清除中断标志位
//     switch( DL_Interrupt_getPendingGroup(DL_INTERRUPT_GROUP_1) )
//     {
//         //检查是否是的GPIOB端口中断，注意是INT_IIDX
//         case Encoder_1_INT_IIDX:
//					if(DL_GPIO_readPins(Encoder_1_PORT, Encoder_1_E1A_PIN) == 1)

//        break;
//    }
//}
// 串口的中断服务函数
void UART_0_INST_IRQHandler(void)
{
    // 如果产生了串口中断
    switch (DL_UART_getPendingInterrupt(UART_0_INST))
    {
    case DL_UART_IIDX_RX: // 如果是接收中断
        // 接发送过来的数据保存在变量中
        uart_data = DL_UART_Main_receiveData(UART_0_INST);
        // 将保存的数据再发送出去
        uart0_send_char(uart_data);
        break;

    default: // 其他的串口中断
        break;
    }
}

// 定时器的中断服务函数 已配置为1ms的周期
void TIMER_0_INST_IRQHandler(void)
{

    static uint32_t timer_count = 1;

    // 如果产生了定时器中断
    if (DL_TimerG_getPendingInterrupt(TIMER_0_INST) == DL_TIMER_IIDX_ZERO) // 如果是0溢出中断
    {
        timer_count = timer_count < 1000 ? timer_count + 1 : 1;
        if (timer_count % 1000 == 0)
            led_flash_time = 1;
    }
}

/****************************End*****************************/
